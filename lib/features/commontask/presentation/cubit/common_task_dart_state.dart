part of 'common_task_dart_cubit.dart';

class CommonTaskState {
  final List<CommonTaskModel> commonTasks;
  final String message;
  final bool isLoading;

  const CommonTaskState({
    required this.commonTasks,
    required this.message,
    required this.isLoading,
  });

  CommonTaskState copyWith({
    List<CommonTaskModel>? commonTasks,
    String? message,
    bool? isLoading,
  }) {
    return CommonTaskState(
      commonTasks: commonTasks ?? this.commonTasks,
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
    );
  }

  factory CommonTaskState.initial() {
    return CommonTaskState(commonTasks: [], message: '', isLoading: false);
  }
}
