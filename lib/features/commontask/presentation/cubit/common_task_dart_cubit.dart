import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cp_associates/features/commontask/domain/entity/commontask_model.dart';
import 'package:cp_associates/features/commontask/domain/repo/coomontask_repo.dart';
import 'package:meta/meta.dart';

part 'common_task_dart_state.dart';

class CommonTaskCubit extends Cubit<CommonTaskState> {
  CommonTaskRepo commonTaskRepo;
  CommonTaskCubit(this.commonTaskRepo) : super(CommonTaskState.initial());
  StreamSubscription<List<CommonTaskModel>>? commonTaskStream;

  void fetchAllCommonTaskOfCurrentUser() {
    emit(state.copyWith(isLoading: true, message: ""));

    commonTaskStream?.cancel();

    commonTaskStream = commonTaskRepo.getAllCommonTaskOfCurrentUser().listen(
      (commonTask) {
        print("AllCommonTaskStream-${commonTask.length}");
        commonTask.sort((a, b) {
          DateTime aDate = a.dueDate;
          DateTime bDate = b.dueDate;
          return aDate.compareTo(bDate);
        });

        emit(
          state.copyWith(
            commonTasks: commonTask,
            isLoading: false,
            message: "",
          ),
        );
      },
      onError: (error) {
        print(error.toString());
        emit(
          state.copyWith(
            isLoading: false,
            message: "Failed to fetch common task: ${error.toString()}",
          ),
        );
      },
    );
  }
}
