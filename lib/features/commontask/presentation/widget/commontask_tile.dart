import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/detail_row_tile.dart';
import 'package:cp_associates/core/widgets/transparent_inkwell.dart';
import 'package:cp_associates/features/admin/domain/entity/admin_task_model.dart';
import 'package:cp_associates/features/admin/presentation/cubit/admin_task_cubit.dart';
import 'package:cp_associates/features/admin/presentation/cubit/admin_task_form_cubit.dart';
import 'package:cp_associates/features/admin/presentation/pages/admin_task_detail.dart';
import 'package:cp_associates/features/admin/presentation/widget/admin_task_form.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/project/domain/entity/project_model.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class CommonTaskTile extends StatefulWidget {
  CommonTaskTile({super.key, required this.isMobile});
  bool isMobile;

  @override
  State<CommonTaskTile> createState() => _CommonTaskTileState();
}

class _CommonTaskTileState extends State<CommonTaskTile> {
  void initState() {
    // context.read<AdminTaskCubit>().fetchAllAdminTask();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final adminTaskCubit = context.read<AdminTaskCubit>();
    final projectCubit = context.read<ProjectCubit>();
    final userCubit = context.read<UserCubit>();
    final authCubit = context.read<AuthCubit>();
    return BlocConsumer<AdminTaskCubit, AdminTaskState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state.adminTasks.isEmpty &&
            state.selectedType == AdminTaskTypes.onGoing) {
          return const Center(child: Text("No Admin Task Avaliable"));
        } else if (state.filteredAdminTasks.isEmpty &&
            state.selectedType == AdminTaskTypes.completed) {
          return const Center(child: Text("No Completed Task Avaliable"));
        } else {
          List<AdminTaskModel> filteredAdminTasks = [];
          if (state.selectedType != AdminTaskTypes.completed) {
            filteredAdminTasks = state.adminTasks;
          } else {
            filteredAdminTasks = state.filteredAdminTasks;
          }
          return SingleChildScrollView(
            child: StaggeredGrid.extent(
              maxCrossAxisExtent: 530,
              mainAxisSpacing: 15,
              crossAxisSpacing: 30,

              children: [
                ...List.generate(filteredAdminTasks.length, (index) {
                  final adminTask = filteredAdminTasks[index];
                  ProjectModel? project = projectCubit.fetchProjectById(
                    adminTask.projectId ?? "",
                  );
                  print("${adminTask.title} :${adminTask.isCompleted}");
                  return TransparentInkWell(
                    onDoubleTap: () {
                      adminTask.isCompleted
                          ? null
                          : widget.isMobile
                          ? showModalBottomSheet(
                            isScrollControlled: true,
                            context: context,
                            builder: (context) {
                              return Padding(
                                padding: MediaQuery.of(context).viewInsets,
                                child: BlocProvider(
                                  create:
                                      (context) => AdminTaskFormCubit(
                                        context
                                            .read<AdminTaskCubit>()
                                            .adminTaskRepo,
                                      ),
                                  child: AdminTaskForm(
                                    editAdminTask: adminTask,
                                  ),
                                ),
                              );
                            },
                          )
                          : showDialog(
                            context: context,
                            builder: (context) {
                              return Dialog(
                                child: Container(
                                  width: 400,
                                  child: BlocProvider(
                                    create:
                                        (context) => AdminTaskFormCubit(
                                          context
                                              .read<AdminTaskCubit>()
                                              .adminTaskRepo,
                                        ),
                                    child: AdminTaskForm(
                                      editAdminTask: adminTask,
                                    ),
                                  ),
                                ),
                              );
                            },
                          );
                    },
                    onTap: () {
                      authCubit.currentUser?.role == "admin" &&
                              !adminTask.isCompleted
                          ? widget.isMobile
                              ? showModalBottomSheet(
                                isScrollControlled: true,
                                useSafeArea: true,
                                context: context,
                                builder: (context) {
                                  return Padding(
                                    padding: MediaQuery.of(context).viewInsets,
                                    child: AdminTaskDetail(
                                      index: index,
                                      adminTask: adminTask,
                                      project: project,
                                    ),
                                  );
                                },
                              )
                              : showDialog(
                                context: context,
                                builder: (context) {
                                  return Dialog(
                                    child: Container(
                                      width: 400,
                                      child: AdminTaskDetail(
                                        index: index,
                                        adminTask: adminTask,
                                        project: project,
                                      ),
                                    ),
                                  );
                                },
                              )
                          : null;
                    },
                    onLongPress: () {
                      showConfirmDeletDialog(context, () {
                        adminTaskCubit.deleteAdminTask(adminTask.docId);
                      });
                    },
                    child: Container(
                      padding: EdgeInsets.all(15),
                      decoration: BoxDecoration(
                        color: AppColors.containerGreyColor,
                        border: Border.all(color: AppColors.borderGrey),
                        borderRadius: BorderRadius.circular(12),
                      ),

                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            // mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  color: getColorFromInput(
                                    (index + 1).toString(),
                                  ),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                height: 45,
                                width: 45,
                                child: Center(
                                  child: Text(
                                    (index + 1).toString(),
                                    // "test",
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 15,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(width: 15),

                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    adminTask.title,
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),

                                  Text(
                                    adminTask.desc,
                                    style: TextStyle(fontSize: 14),
                                  ),
                                ],
                              ),
                              Spacer(),
                              adminTask.isCompleted
                                  ? Icon(
                                    CupertinoIcons.check_mark_circled,
                                    color: Colors.green,
                                  )
                                  : Icon(
                                    CupertinoIcons.chevron_right,
                                    size: 20,
                                  ),
                            ],
                          ),
                          SizedBox(height: 15),
                          DetailRowTile(
                            title: "Project",
                            value: project?.projectTitle ?? "-",
                          ),

                          SizedBox(height: 5),
                          Row(
                            children: [
                              Text(
                                "Due Date",
                                style: TextStyle(
                                  color: AppColors.grey2,
                                  fontSize: 14,
                                ),
                              ),
                              Spacer(),
                              Text(
                                adminTask.dueDate != null
                                    ? adminTask.dueDate!.hour == 0 &&
                                            adminTask.dueDate!.minute == 0 &&
                                            adminTask.dueDate!.second == 0
                                        ? adminTask.dueDate!.goodDayDate()
                                        : adminTask.dueDate!.goodDayDateTime()
                                    : "-",

                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color:
                                      adminTask.dueDate != null
                                          ? getDueDateColor(
                                            adminTask.dueDate!,
                                            adminTask.isCompleted,
                                          )
                                          : Colors.black,
                                ),
                              ),
                            ],
                          ),

                          SizedBox(height: 5),

                          Row(
                            children: [
                              Text(
                                "Created",
                                style: TextStyle(
                                  color: AppColors.grey2,
                                  fontSize: 14,
                                ),
                              ),
                              Spacer(),
                              Text(
                                getTimeAgo(adminTask.createdAt) ?? "",
                                style: TextStyle(fontWeight: FontWeight.w600),
                              ),
                              SizedBox(width: 5),
                              Container(
                                padding: EdgeInsets.all(2),
                                decoration: BoxDecoration(
                                  // shape: BoxShape.circle,
                                  color: AppColors.black,
                                  // borderRadius: BorderRadius.circular(4),
                                  shape: BoxShape.circle,
                                ),
                              ),
                              SizedBox(width: 5),

                              Text(
                                userCubit
                                        .getUserById(adminTask.createdBy)
                                        ?.name ??
                                    "",
                                style: TextStyle(fontWeight: FontWeight.w600),
                              ),
                            ],
                          ),
                          SizedBox(height: 5),

                          SizedBox(height: 5),
                          adminTask.completedAt != null
                              ? DetailRowTile(
                                title: "Completed At",
                                value: adminTask.completedAt?.goodDayDateTime(),
                              )
                              : SizedBox(),
                        ],
                      ),
                    ),
                  );
                }),
                SizedBox(height: 30),
              ],
            ),
          );
        }
      },
    );
  }
}
