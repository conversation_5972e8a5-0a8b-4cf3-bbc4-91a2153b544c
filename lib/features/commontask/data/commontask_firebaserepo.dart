import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/commontask/domain/entity/commontask_model.dart';
import 'package:cp_associates/features/commontask/domain/repo/coomontask_repo.dart';

class FirebaseCommonTaskRepo implements CommonTaskRepo {
  final commonTaskRef = FBFireStore.commonTasks;

  // CRUD Functions
  @override
  Future<void> createCommonTask(CommonTaskModel commonTask) async {
    final docRef = commonTaskRef.doc();
    final newCommonTask = commonTask.copyWith(docId: docRef.id);
    await docRef.set(newCommonTask.toJson());
  }

  @override
  Future<void> updateCommonTask(CommonTaskModel commonTask) async {
    await commonTaskRef.doc(commonTask.docId).update(commonTask.toJson());
  }

  @override
  Future<void> deleteAdminTask(String commonTaskId) async {
    await commonTaskRef.doc(commonTaskId).delete();
  }

  // Fetch Methods
  @override
  Stream<List<CommonTaskModel>> getAllCommonTaskOfCurrentUser() {
    return commonTaskRef
        .orderBy('createdAt', descending: true)
        .where('isCompleted', isEqualTo: false)
        .where('assignTo', isEqualTo: FBAuth.auth.currentUser?.uid ?? '')
        .where('createdBy', isEqualTo: FBAuth.auth.currentUser?.uid ?? '')
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => CommonTaskModel.fromSnapshot(doc))
              .toList();
        });
  }

  @override
  Future<List<CommonTaskModel>> getCompletedTask(DateTime month) async {
    final startOfMonth = DateTime(month.year, month.month);
    final endOfMonth = DateTime(month.year, month.month + 1);

    final query =
        await commonTaskRef
            .orderBy('completedAt', descending: true)
            .where('isCompleted', isEqualTo: true)
            .where('completedAt', isGreaterThanOrEqualTo: startOfMonth)
            .where('completedAt', isLessThan: endOfMonth)
            .where('assignTo', isEqualTo: FBAuth.auth.currentUser?.uid ?? '')
            .where('createdBy', isEqualTo: FBAuth.auth.currentUser?.uid ?? '')
            .get();

    return query.docs.map((doc) => CommonTaskModel.fromSnapshot(doc)).toList();
  }
}
