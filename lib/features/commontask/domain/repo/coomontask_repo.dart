import 'package:cp_associates/features/admin/domain/entity/admin_task_model.dart';
import 'package:cp_associates/features/commontask/domain/entity/commontask_model.dart';

abstract class CommonTaskRepo {
  Future<void> createCommonTask(CommonTaskModel commonTask);
  Future<void> updateCommonTask(CommonTaskModel commonTask);
  Future<void> deleteAdminTask(String commonTaskId);
  Stream<List<CommonTaskModel>> getAllCommonTaskOfCurrentUser();
  Future<List<CommonTaskModel>> getCompletedTask(DateTime month);
}
